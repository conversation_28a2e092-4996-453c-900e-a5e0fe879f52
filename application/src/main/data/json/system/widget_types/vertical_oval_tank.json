{"fqn": "vertical_oval_tank", "name": "Vertical oval tank", "deprecated": false, "image": "tb-image;/api/images/system/vertical_oval_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Vertical oval tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Vertical Oval\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"rgba(255, 255, 255, 0.76)\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/vertical_oval_tank_system_widget_image.png", "title": "\"Vertical oval tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vertical_oval_tank_system_widget_image.png", "publicResourceKey": "ke5HyRMFjq03RoPVl2ZI41l5CdIkFNGe", "mediaType": "image/png", "data": "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", "public": true}]}