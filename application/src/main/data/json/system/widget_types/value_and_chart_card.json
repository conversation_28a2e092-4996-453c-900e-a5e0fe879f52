{"fqn": "cards.aggregated_value_card", "name": "Value and chart card", "deprecated": false, "image": "tb-image;/api/images/system/value_and_chart_card_system_widget_image.png", "description": "Displays a single entity telemetry as a combination of the latest and aggregated values. Optionally may display the corresponding historical values as a simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'watermeter', label: 'Watermeter', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'watermeter', 'm³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"watermeter\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 5) {\\n\\tvalue = 5;\\n} else if (value > 80) {\\n\\tvalue = 80;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 5) {\\n\\tvalue = 5;\\n} else if (value > 80) {\\n\\tvalue = 80;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m³\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Value and chart card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"water_drop\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "resources": [{"link": "/api/images/system/value_and_chart_card_system_widget_image.png", "title": "\"Value and chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "value_and_chart_card_system_widget_image.png", "publicResourceKey": "07BpFixQWQ57FZzYOjshKmE4z2AFi5DQ", "mediaType": "image/png", "data": "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", "public": true}]}