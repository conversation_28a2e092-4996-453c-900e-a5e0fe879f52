{"fqn": "noise_level_card", "name": "Noise level card", "deprecated": false, "image": "tb-image;/api/images/system/noise_level_card_system_widget_image.png", "description": "Displays the latest noise level telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'noise', label: 'Noise level ', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Noise level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value;\\nif (!prevValue) {\\n    value = Math.random() * 120;\\n} else {\\n    value = prevValue + Math.random() * 40 - 20;\\n}\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bar_chart\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":70,\"color\":\"#FFA600\"},{\"from\":70,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":70,\"color\":\"#FFA600\"},{\"from\":70,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Noise level card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"dB\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "sound level", "acoustic level", "decibel level", "volume", "loudness", "ambient noise", "sound intensity", "acoustic intensity"], "resources": [{"link": "/api/images/system/noise_level_card_system_widget_image.png", "title": "\"Noise level card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "noise_level_card_system_widget_image.png", "publicResourceKey": "6VHIBSeXeW7cSO9kZOj2OBE7LyKanF8P", "mediaType": "image/png", "data": "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", "public": true}]}