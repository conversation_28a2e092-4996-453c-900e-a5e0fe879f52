{"fqn": "flooding_level_card", "name": "Flooding level card", "deprecated": false, "image": "tb-image;/api/images/system/flooding_level_card_system_widget_image.png", "description": "Displays the latest flooding level telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flooding', label: 'Flooding level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"flood\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Flooding level card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "flood", "flooding", "water height", "flood depth", "flood stage", "inundation level", "water rise", "overflow level", "flood peak", "high water mark"], "resources": [{"link": "/api/images/system/flooding_level_card_system_widget_image.png", "title": "\"Flooding level card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "flooding_level_card_system_widget_image.png", "publicResourceKey": "x8537bP2e3GYfAVbZQHgq38NgAYJ9ZMP", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEXg4ODf39/g4ODg4OAAAAD///8jTMfh4eGrq6shISGtvOrk6fiRpeNaeNXI0vHIyMg+Ys63t7c9PT0/Ys7z8/MwV8vy9Pt1j9zOzs7n5+dYWFj5+fl0dHTV1dWwsLCQkJDx8fHu7u6Dmt+kpKTCwsKfsee8vLzb29tohNlMbtGCgoIvLy/W3vW6x+6enp5KSkqRpuNmZmYxV8qdnZ2QpeN/GPn1AAAABXRSTlPvIL+vAC9A4IoAAAZ7SURBVHja7M8BDQAwCAOwcSDYAP8mb2OQ1UER9lDbuQUMk+s1DN55wDxUnoArkVKEjCJsFGGjCBtF2CjCRhE2inzqyXDFVRgIo78mceraaEorBCVhoWU32avr7fu/2x2ntlu13kIh4B6Q0YnCdxhnbTwVOcDv4JnIIc/LFH4Bz0RKQWSbR3Nxsgdq62COtHQiYU7RwCOcraOKfIuBcgtTEuxRCUqYo1ABVjCn0o9zYBJT5I/4IT+mUxFFE3HLIsqtRSTdixHZZiwir0V63fZBzrqtHBdPIiaBxlttJHcs1UGkNrqS0DUA3oM02tRxRdJcTMnfJhNxLKJ14ZFio020oVA+aUkELVRoi9CB4s5VRAZftAaM4fi6Lbrgoop8iDmbyY6YXkRhAX2utgM4o7MIcBpENEf3gV6/iVzOZYGOv1XgMIkp8i2mHDd5Ot0RKvKENSdEy8tRaSojEWPuRSrsUS40XUftHhtR5ChmvMPjHamHieiKH32YiHD5EaFzpjIhoXYdc9l50WfkCyKg27qhJDYkShuSsKobiXDnJqLQ1yfjqAYHjr5VJsqOzBd9n30KJoU7Ci2vRRoMDd37gEYCCYVKK9Bn8Abo4o5hEX4q9OX11vcJWgwWpC7iiFDyYSTZ2/YAx+uqv0hNLuggFssiX3vxsRk2nAckmPJVDzQGLURk+dfK0lH4y3j+wovIs1UwJ74Is7sLXwrmC9bK/0TeBbMF4vafrZVFkW25ywXzucuO6UEwGayVBZFtJkbk5X642cA6WRB5o8zXiez/sWeGLW7DMBj+pFlcm6UmTiZjbHDtORfYwv7/r5vUpNddYR/K3RV/8EuqvLbU0AcVF9Rl3E33aE+c/X8umueAdN8BXuTX5Dzx8sTmOMHhx3gD6f3doxzcy+IONP8DRwQiPT8DZJr2+wHuDN93ZZLoouPIweEsSyOX4Q1JWZTIQKsBMPziqEMGWQiI1G/PMGKfPw66gRgsGA1m9JSCkx7IpYNGYzAEBMLC6zVDH5BARAwyJ50EZOasuxTKFrovAHnphuH1F8D0ZxkWGaQczsOw/LwHicoUiskYC2jhDWSG4pUGxatoOLsC18T0BhIsBAaRLFoqUoiR3/PpIL+7/bi9nlavuzkd34Oo0AeCWeM9CGXKYinQBUStWmt3BUlGauCStZmkcDWc+YpTa1x2mLE7bWYQ070HyT0UsgS+ACrpUALFIARhtheLausIR7DXjmwF7INyyXoNHrcmfT7IeJZvUzd+G46THF/b5GE6LjeQHhF9RNTkMoYIfpUPWlLRoEPSDnqxHkuwDgMQV4PI98yLWJT4pLVyJekgW7m62e9jR6ubodd1DrF7+xBIDtrUCfKw2t8KDaRaNZDa1EBqUwOpTQ2kNjWQ2tRAalMDqU0NpDY1kNrUQGpTA6lNDaQ2NZDa9AGQ6S/7ZtBiIQgE4NPMwUkJIr0HQYFCxP7/37Y+ZXH02R7beUvfbWKI+RrLEguDc4Mi6ONNDTRIEbHHjBndV5mxYoQaKSI0YuGADvgRItGDEzoZnyHiMDIPwahkdFpoMRiZChpqZIgQRkYq25yH/rZ6C9eIEFnZTk07v6T6KXCNDBHNLnduCUHDUuldY4lKQGTvFcGIbtrTsJWUDkZHPNDyaufsKB9LvXV0n4jHiKtu66HXtA2uUBgJK/6gwEbzzGluEmlLp6LFmHKKJbJXIjsWDAtOuktEvYtsvdH3dUzYHy0KM5PWmJh5sPyhiIYaixVHV2Q0AEALtsEpSMTgryaKTUSQ+0DsFc3LEQl5wGxuGzFhrs+QuuB4EOSI+JfAYlN2UtHXZ1D8+T2kQI5IhDx/WqP/WJHmFXP9ByIBI06gSOjNI124qkARj5Glja6xUkXqHhiMrB/ZEag+QQY+TXC5pYo2iSKO1c4/TtrBxNOVRJHALrHhNRNxO8NXKkiiiJ0xcpQqVa5YI45UijtVyXASZ/afH3nHZdHIGuLYnbHjiz1mJGkkmSLgkDESK37vrnytIFQEBu7Re40l5jorECsC5PKY0Ws5tFcrwWbLGdNgoSFMkZUHATJrCu4R4Qvuvq7R0nsGQQ8Z61oCeUSk8YhI4xGRxiMijUfku707IAIghIEYeAV+agP/Kt9G6GQdREFoDKExhMYQGkNoDKExhMYQGkNoDKExhMYQGkNoDKExhMYQGkNoDKHp5Buhs0Ysm+9OZUDJTeXU7tdl1fkBRtfYgo4/u3IAAAAASUVORK5CYII=", "public": true}]}