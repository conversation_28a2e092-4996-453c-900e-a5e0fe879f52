/**
 * Copyright © 2016-2025 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.settings;

import lombok.Getter;

public enum UserSettingsType {

    GENERAL,
    VISITED_DASHBOARDS(true),
    QUICK_LINKS,
    DOC_LINKS,
    <PERSON>ASHBOARDS,
    GETTING_STARTED,
    NOTIFICATIONS,
    MOBILE(true);

    @Getter
    private final boolean reserved;

    UserSettingsType() {
        this.reserved = false;
    }

    UserSettingsType(boolean reserved) {
        this.reserved = reserved;
    }
}
