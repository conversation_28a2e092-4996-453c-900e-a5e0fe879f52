<?xml version="1.0" encoding="UTF-8" ?>
<!--

    Copyright © 2016-2025 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<!DOCTYPE configuration>
<configuration scan="true" scanPeriod="10 seconds">

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.thingsboard.server" level="INFO" />

    <logger name="com.microsoft.azure.servicebus.primitives.CoreMessageReceiver" level="OFF" />
    <logger name="org.apache.kafka.common.utils.AppInfoParser" level="WARN"/>
    <logger name="org.apache.kafka.clients" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>