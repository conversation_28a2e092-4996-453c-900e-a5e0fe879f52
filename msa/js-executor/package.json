{"name": "thingsboard-js-executor", "private": true, "version": "4.2.0", "description": "ThingsBoard JavaScript Executor Microservice", "main": "server.ts", "bin": "server.js", "scripts": {"pkg": "tsc && pkg -t node18-linux-x64,node18-win-x64 --out-path ./target ./target/src && node install.js", "test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon --watch '.' --ext 'ts' --exec 'ts-node server.ts'", "start-prod": "nodemon --watch '.' --ext 'ts' --exec 'NODE_ENV=production ts-node server.ts'", "build": "tsc"}, "dependencies": {"config": "^3.3.12", "express": "^4.21.1", "js-yaml": "^4.1.0", "kafkajs": "^2.2.4", "long": "^5.2.3", "uuid-parse": "^1.1.0", "winston": "^3.16.0", "winston-daily-rotate-file": "^5.0.0"}, "nyc": {"exclude": ["test", "__tests__", "node_modules", "target"]}, "devDependencies": {"@types/config": "^3.3.5", "@types/express": "~4.17.21", "@types/node": "~20.17.6", "@types/uuid-parse": "^1.0.2", "fs-extra": "^11.2.0", "nodemon": "^3.1.7", "pkg": "^5.8.1", "ts-node": "^10.9.2", "typescript": "5.5.4"}, "pkg": {"assets": ["node_modules/config/**/*.*"]}}